const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
const http = require('http');
const socketIo = require('socket.io');

// Import MySQL configuration
const { sequelize, testConnection } = require('./models/mysql');

// Load environment variables
require('dotenv').config();

// Import routes
const whatsappRoutes = require('./routes/whatsapp');
const authRoutes = require('./routes/auth');
const conversationRoutes = require('./routes/conversations');
const analyticsRoutes = require('./routes/analytics');
const agentRoutes = require('./routes/agents');
const chatRoutes = require('./routes/chat');

// Import middleware
const errorHandler = require('./middleware/errorHandler');
const { logger } = require('./utils/logger');

// Import services
const cronJobs = require('./services/cronJobs');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production' ? false : "http://localhost:3001",
    methods: ["GET", "POST"]
  }
});

// Make io accessible to routes
app.set('io', io);

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests from this IP, please try again later.'
  }
});

app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://your-domain.com'] 
    : ['http://localhost:3001'],
  credentials: true
}));

// Content Security Policy pour permettre les scripts externes
app.use((req, res, next) => {
  res.setHeader('Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " +
    "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " +
    "font-src 'self' https://cdnjs.cloudflare.com; " +
    "img-src 'self' data: https:; " +
    "connect-src 'self';"
  );
  next();
});

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use(express.static(path.join(__dirname, 'public')));

// API Routes
app.use('/api/whatsapp', whatsappRoutes);
app.use('/api/auth', authRoutes);
app.use('/api/conversations', conversationRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/agents', agentRoutes);
app.use('/api/chat', chatRoutes);

// Route pour collecter les actualités manuellement
app.post('/api/news/collect', async (req, res) => {
  try {
    const FocepNewsAlternative = require('./services/focepNewsAlternative');
    const newsService = new FocepNewsAlternative();
    const result = await newsService.collectAllNews();

    res.json({
      success: true,
      message: 'Collecte d\'actualités déclenchée',
      data: result
    });
  } catch (error) {
    logger.error('Erreur collecte manuelle actualités:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la collecte d\'actualités'
    });
  }
});

// Routes WhatsApp Web
let whatsappService = null;

// Initialiser WhatsApp Web
app.post('/api/whatsapp/start', async (req, res) => {
  try {
    if (whatsappService && whatsappService.isReady) {
      return res.json({
        success: true,
        message: 'WhatsApp Web déjà connecté',
        data: whatsappService.getConnectionInfo()
      });
    }

    const WhatsAppWebService = require('./services/whatsappWebService');
    whatsappService = new WhatsAppWebService();

    // Démarrer en arrière-plan
    whatsappService.initialize().catch(error => {
      logger.error('Erreur initialisation WhatsApp Web:', error);
    });

    res.json({
      success: true,
      message: 'Initialisation WhatsApp Web démarrée',
      data: {
        status: 'initializing',
        qrCode: null
      }
    });

  } catch (error) {
    logger.error('Erreur démarrage WhatsApp Web:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors du démarrage de WhatsApp Web'
    });
  }
});

// Obtenir le statut WhatsApp Web
app.get('/api/whatsapp/status', (req, res) => {
  try {
    if (!whatsappService) {
      return res.json({
        success: true,
        data: {
          isReady: false,
          status: 'not_initialized',
          qrCode: null
        }
      });
    }

    const info = whatsappService.getConnectionInfo();
    const stats = whatsappService.getStats();

    res.json({
      success: true,
      data: {
        ...info,
        stats
      }
    });

  } catch (error) {
    logger.error('Erreur statut WhatsApp Web:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la récupération du statut'
    });
  }
});

// Envoyer un message via WhatsApp Web
app.post('/api/whatsapp/send', async (req, res) => {
  try {
    const { phoneNumber, message } = req.body;

    if (!phoneNumber || !message) {
      return res.status(400).json({
        success: false,
        error: 'Numéro de téléphone et message requis'
      });
    }

    if (!whatsappService || !whatsappService.isReady) {
      return res.status(400).json({
        success: false,
        error: 'WhatsApp Web n\'est pas connecté'
      });
    }

    const success = await whatsappService.sendMessage(phoneNumber, message);

    res.json({
      success,
      message: success ? 'Message envoyé avec succès' : 'Échec de l\'envoi du message'
    });

  } catch (error) {
    logger.error('Erreur envoi message WhatsApp:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de l\'envoi du message'
    });
  }
});

// Arrêter WhatsApp Web
app.post('/api/whatsapp/stop', async (req, res) => {
  try {
    if (whatsappService) {
      await whatsappService.disconnect();
      whatsappService = null;
    }

    res.json({
      success: true,
      message: 'WhatsApp Web déconnecté'
    });

  } catch (error) {
    logger.error('Erreur arrêt WhatsApp Web:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de l\'arrêt de WhatsApp Web'
    });
  }
});

// Route webhook pour le chatbot (endpoint principal)
app.post('/webhook', async (req, res) => {
  try {
    const { messages } = req.body;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Messages requis'
      });
    }

    const message = messages[0];
    const { from, body } = message;

    if (!from || !body) {
      return res.status(400).json({
        success: false,
        error: 'Numéro et message requis'
      });
    }

    logger.info('Webhook reçu:', { from, body });

    // Traitement simple pour les tests
    const startTime = Date.now();

    // Générer une réponse simple basée sur le message
    let responseText = '';
    const lowerBody = body.toLowerCase();

    if (lowerBody.includes('focep') || lowerBody.includes('qu\'est-ce que')) {
      responseText = `🏦 **FOCEP SA - Fonds Camerounais d'Épargne et de Crédit pour la Production**

Bonjour ! 🌞 FOCEP SA est votre partenaire de confiance en microfinance au Cameroun depuis plus de 20 ans.

🎯 **Nos services :**
• 💳 Crédits (commercial, agricole, jeunes entrepreneurs)
• 💰 Comptes d'épargne sécurisés
• 📱 Services digitaux (application mobile)
• 🎓 Éducation financière

📍 **Nos agences :** Yaoundé, Douala, Bafoussam et 15+ autres villes
📞 **Contact :** +237 222 23 14 83

Comment puis-je vous aider aujourd'hui ? 😊`;
    } else if (lowerBody.includes('actualité') || lowerBody.includes('nouvelle')) {
      responseText = `📰 **Dernières Actualités FOCEP**

🎉 **Nouveautés récentes :**
1. 💳 Nouveau crédit jeunes entrepreneurs (taux 10%)
2. 🏢 3 nouvelles agences ouvertes (Nord, Adamaoua, Est)
3. 📱 Application mobile lancée
4. 🎓 5000 bénéficiaires formés en éducation financière

💡 Ces actualités sont mises à jour en temps réel grâce à notre système de veille intelligent.

Souhaitez-vous plus de détails sur l'une de ces actualités ? 📋`;
    } else if (lowerBody.includes('crédit') || lowerBody.includes('prêt')) {
      responseText = `💳 **Nos Solutions de Crédit FOCEP**

🌟 **Produits disponibles :**
• **Crédit Commercial** - Taux 12%, jusqu'à 24 mois
• **Crédit Agricole** - Conditions spéciales agriculteurs
• **Crédit Jeunes** - Nouveau ! Taux préférentiel 10%

📋 **Documents requis :**
• Pièce d'identité
• Justificatifs de revenus
• Plan d'affaires (pour crédit commercial)

📞 **Pour une demande :** Contactez-nous au +237 222 23 14 83

Quel type de crédit vous intéresse ? 🤔`;
    } else {
      responseText = `Bonjour ! 🌞 Merci pour votre message.

Je suis l'assistant virtuel de FOCEP SA, votre institution de microfinance de confiance au Cameroun.

💡 **Je peux vous aider avec :**
• Informations sur FOCEP SA
• Nos produits de crédit et d'épargne
• Localisation de nos agences
• Dernières actualités

Comment puis-je vous aider aujourd'hui ? 😊

📞 Contact direct : +237 222 23 14 83`;
    }

    const response = {
      type: 'text',
      content: {
        text: responseText
      }
    };

    const responseTime = Date.now() - startTime;

    logger.info(`Message traité en ${responseTime}ms`, {
      from,
      messageLength: body.length,
      responseTime
    });

    res.json({
      success: true,
      data: {
        response,
        responseTime,
        from,
        processedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error('Erreur webhook chatbot:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors du traitement du message',
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV
  });
});

// Serve React app in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'client/build')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
  });
}

// Error handling middleware
app.use(errorHandler);

// Socket.io connection handling
io.on('connection', (socket) => {
  logger.info(`Agent connected: ${socket.id}`);
  
  socket.on('join-agent-room', (agentId) => {
    socket.join(`agent-${agentId}`);
    logger.info(`Agent ${agentId} joined room`);
  });
  
  socket.on('disconnect', () => {
    logger.info(`Agent disconnected: ${socket.id}`);
  });
});

// Database connection
async function startServer() {
  try {
    // Test MySQL connection
    await testConnection();
    logger.info('Connected to MySQL database');

    // Start cron jobs
    cronJobs.initializeJobs();

    // Start server
    const PORT = process.env.PORT || 3000;
    server.listen(PORT, () => {
      logger.info(`FOCEP WhatsApp Chatbot server running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV}`);
      logger.info(`Database: MySQL`);
    });
  } catch (error) {
    logger.error('Database connection error:', error);
    process.exit(1);
  }
}

// Start the server
startServer();

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    mongoose.connection.close();
    process.exit(0);
  });
});

module.exports = app;
