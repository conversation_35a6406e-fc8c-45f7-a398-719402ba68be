# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration (MySQL Primary)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=focep_chatbot
DB_USER=root
DB_PASSWORD=your_mysql_password

# Database Configuration (MongoDB Alternative)
MONGODB_URI=mongodb://localhost:27017/focep-chatbot
MONGODB_TEST_URI=mongodb://localhost:27017/focep-chatbot-test

# WhatsApp Business API Configuration
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id_here
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token_here
WHATSAPP_WEBHOOK_SECRET=your_webhook_secret_here

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=7d

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123456

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# External APIs
GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key
OPENAI_API_KEY=your_openai_api_key_optional

# DeepSeek AI Configuration
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_MAX_TOKENS=1000
DEEPSEEK_TEMPERATURE=0.7
DEEPSEEK_TIMEOUT=30000
DEEPSEEK_ENABLED=true

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Analytics
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=90

# Business Hours (24-hour format)
BUSINESS_HOURS_START=08:00
BUSINESS_HOURS_END=17:00
BUSINESS_DAYS=1,2,3,4,5

# FOCEP Specific Configuration
FOCEP_MAIN_PHONE=+237222231483
FOCEP_EMAIL=<EMAIL>
FOCEP_WEBSITE=https://focep.cm
FOCEP_INTEREST_RATE=4
FOCEP_COMPANY_NAME=FOCEP SA
FOCEP_FULL_NAME=Fonds Camerounais d'Épargne pour le Progrès

# Mobile Money Configuration
ORANGE_MONEY_ENABLED=true
MTN_MOBILE_MONEY_ENABLED=true
MOBILE_MONEY_MAX_AMOUNT=500000

# SMS Configuration for balance inquiries
SMS_BALANCE_CODE=SOLDE
SMS_SHORT_CODE=8585
SMS_PROVIDER_API_KEY=your_sms_provider_api_key

# Microinsurance Configuration
MICROINSURANCE_ENABLED=true
LIFE_INSURANCE_RATE=0.5
HEALTH_INSURANCE_RATE=1.0
CROP_INSURANCE_RATE=2.0

# Credit Configuration
CREDIT_PROCESSING_DAYS_CIVIL_SERVANTS=3
CREDIT_PROCESSING_DAYS_MERCHANTS=5
CREDIT_PROCESSING_DAYS_ENTREPRENEURS=7
CREDIT_PROCESSING_DAYS_AGRICULTURAL=10

# Regional Configuration
SUPPORTED_REGIONS=Centre,Littoral,Ouest,Nord
DEFAULT_REGION=Centre

# Language and Localization
DEFAULT_LANGUAGE=fr
SUPPORTED_LANGUAGES=fr,en
CURRENCY_SYMBOL=FCFA
TIMEZONE=Africa/Douala

# Notification Templates
WELCOME_MESSAGE_TEMPLATE=Bienvenue chez FOCEP SA! Comment puis-je vous aider?
ESCALATION_MESSAGE_TEMPLATE=Un agent va vous contacter sous peu.
BUSINESS_HOURS_MESSAGE=Nos agences sont ouvertes du lundi au vendredi de 8h à 17h, samedi de 8h à 12h.

# Security Configuration
SESSION_SECRET=your_session_secret_here
ENCRYPTION_KEY=your_encryption_key_here
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=30

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL=3600
REDIS_URL=redis://localhost:6379

# Backup Configuration
AUTO_BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=./backups

# Monitoring and Analytics
PERFORMANCE_MONITORING=true
ERROR_TRACKING_ENABLED=true
ANALYTICS_TRACKING_ID=your_analytics_tracking_id
SENTRY_DSN=your_sentry_dsn

# Development Configuration
DEBUG_MODE=true
MOCK_WHATSAPP_API=false
MOCK_SMS_API=false
MOCK_EMAIL_API=false
ENABLE_API_DOCS=true
