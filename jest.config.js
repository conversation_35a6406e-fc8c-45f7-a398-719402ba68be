module.exports = {
  // Environnement de test
  testEnvironment: 'node',
  
  // Répertoires de test
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*.spec.js'
  ],
  
  // Fichiers à ignorer
  testPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    '/dist/',
    '/build/'
  ],
  
  // Configuration de la couverture de code
  collectCoverage: true,
  collectCoverageFrom: [
    'services/**/*.js',
    'routes/**/*.js',
    'models/**/*.js',
    'utils/**/*.js',
    'config/**/*.js',
    '!**/node_modules/**',
    '!**/coverage/**',
    '!**/tests/**',
    '!**/dist/**',
    '!**/build/**'
  ],
  
  // Seuils de couverture (réduits pour les tests initiaux)
  coverageThreshold: {
    global: {
      branches: 30,
      functions: 40,
      lines: 50,
      statements: 50
    }
  },
  
  // Répertoire de sortie pour la couverture
  coverageDirectory: 'coverage',
  
  // Formats de rapport de couverture
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'json'
  ],
  
  // Configuration des timeouts
  testTimeout: 30000, // 30 secondes pour les tests d'intégration

  // Variables d'environnement pour les tests
  setupFilesAfterEnv: ['<rootDir>/tests/setupSequelize.js'],
  
  // Extensions de fichiers à traiter
  moduleFileExtensions: ['js', 'json'],
  
  // Chemins de modules
  moduleDirectories: ['node_modules', '<rootDir>'],
  
  // Alias de modules
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
    '^@services/(.*)$': '<rootDir>/services/$1',
    '^@models/(.*)$': '<rootDir>/models/$1',
    '^@utils/(.*)$': '<rootDir>/utils/$1',
    '^@config/(.*)$': '<rootDir>/config/$1',
    '^@data/(.*)$': '<rootDir>/data/$1'
  },
  
  // Configuration pour les tests en parallèle
  maxWorkers: '50%',
  
  // Nettoyage automatique des mocks
  clearMocks: true,
  restoreMocks: true,
  
  // Affichage détaillé
  verbose: true,
  
  // Détection des handles ouverts
  detectOpenHandles: true,
  forceExit: true,
  
  // Configuration des reporters
  reporters: ['default'],
  
  // Configuration globale pour les tests
  globals: {
    'process.env': {
      NODE_ENV: 'test',
      DB_NAME: 'focep_test',
      DB_HOST: 'localhost',
      DB_USER: 'test_user',
      DB_PASS: 'test_password',
      JWT_SECRET: 'test_jwt_secret_key_for_testing_only',
      WHATSAPP_TOKEN: 'test_whatsapp_token',
      WHATSAPP_WEBHOOK_VERIFY_TOKEN: 'test_verify_token',
      WHATSAPP_PHONE_NUMBER_ID: 'test_phone_id',
      REDIS_URL: 'redis://localhost:6379/1'
    }
  },
  
  // Configuration des mocks
  // setupFiles: ['<rootDir>/tests/mocks/setup.js'],
  
  // Patterns pour les tests de différents types
  projects: [
    {
      displayName: 'Unit Tests',
      testMatch: ['<rootDir>/tests/unit/**/*.test.js'],
      testEnvironment: 'node'
    },
    {
      displayName: 'Integration Tests',
      testMatch: ['<rootDir>/tests/integration/**/*.test.js'],
      testEnvironment: 'node',
      // setupFilesAfterEnv: ['<rootDir>/tests/integration/setup.js']
    }
  ],
  
  // Configuration pour les tests de performance
  slowTestThreshold: 5000, // 5 secondes
  
  // Gestion des erreurs non capturées
  errorOnDeprecated: true,
  
  // Configuration pour les snapshots
  snapshotSerializers: [],
  
  // Fichiers à exécuter avant tous les tests
  // globalSetup: '<rootDir>/tests/globalSetup.js',
  // globalTeardown: '<rootDir>/tests/globalTeardown.js'
};
