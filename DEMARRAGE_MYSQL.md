# 🚀 Guide de Démarrage Rapide - FOCEP Chatbot avec MySQL

## 📋 **Prérequis**

### **1. Logiciels requis**
- ✅ Node.js 16+ et npm 8+
- ✅ MySQL 8.0+ ou MariaDB 10.5+
- ✅ Un éditeur de code (VS Code recommandé)

### **2. Compte WhatsApp Business API (optionnel pour les tests)**
- Compte Facebook Developers
- Application WhatsApp Business API configurée

## 🔧 **Installation Étape par Étape**

### **Étape 1 : Cloner et installer**
```cmd
# Cloner le projet (si pas déjà fait)
git clone <votre-repo>
cd focep-whatsapp-chatbot

# Installer les dépendances
npm install
```

### **Étape 2 : Configurer MySQL**

#### **Option A : MySQL local**
```cmd
# Installer MySQL (si pas déjà fait)
# Télécharger depuis: https://dev.mysql.com/downloads/mysql/

# Ou avec chocolatey:
choco install mysql

# Démarrer MySQL
net start mysql
```

#### **Option B : XAMPP/WAMP**
- Démarrer XAMPP/WAMP
- Activer MySQL dans le panneau de contrôle

### **Étape 3 : Créer la base de données**
```sql
-- Se connecter à MySQL et exécuter:
CREATE DATABASE focep_chatbot CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE focep_chatbot_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Créer un utilisateur (optionnel)
CREATE USER 'focep_user'@'localhost' IDENTIFIED BY 'focep_password';
GRANT ALL PRIVILEGES ON focep_chatbot.* TO 'focep_user'@'localhost';
GRANT ALL PRIVILEGES ON focep_chatbot_test.* TO 'focep_user'@'localhost';
FLUSH PRIVILEGES;
```

### **Étape 4 : Configurer les variables d'environnement**
```cmd
# Copier le fichier de configuration
copy .env.example .env
```

**Éditer le fichier `.env` :**
```env
# Database Configuration (IMPORTANT: Modifier ces valeurs)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=focep_chatbot
DB_USER=root
DB_PASSWORD=votre_mot_de_passe_mysql
DB_DIALECT=mysql

# Test Database
DB_TEST_NAME=focep_chatbot_test

# Autres configurations...
```

### **Étape 5 : Initialiser la base de données**
```cmd
# Créer les tables et insérer les données FOCEP
npm run init-db

# Ou nettoyer et réinitialiser complètement
npm run init-db:clean
```

**Vous devriez voir :**
```
🚀 Initialisation de la base de données MySQL FOCEP...
✅ Connexion MySQL établie avec succès
📦 Initialisation des produits FOCEP...
  ✅ Produit créé: Compte d'épargne
  ✅ Produit créé: Crédits aux jeunes entrepreneurs
🏢 Initialisation des agences FOCEP...
  ✅ Agence créée: FOCEP Yaoundé Centre
👤 Création de l'agent administrateur...
  ✅ Agent admin créé: <EMAIL>
🎉 Initialisation terminée avec succès !
```

### **Étape 6 : Démarrer l'application**
```cmd
# Mode développement avec rechargement automatique
npm run dev
```

**Vous devriez voir :**
```
✅ Connected to MySQL database
🚀 FOCEP WhatsApp Chatbot server running on port 3000
Environment: development
Database: MySQL
```

## 🧪 **Tester l'Installation**

### **1. Vérifier la base de données**
```sql
-- Se connecter à MySQL et vérifier:
USE focep_chatbot;
SHOW TABLES;

-- Vérifier les données
SELECT COUNT(*) FROM products;
SELECT COUNT(*) FROM agencies;
SELECT COUNT(*) FROM agents;
```

### **2. Tester l'API**
```cmd
# Dans un nouveau terminal, tester l'API
curl http://localhost:3000/api/health
```

### **3. Tester l'authentification**
```cmd
curl -X POST http://localhost:3000/api/auth/login ^
  -H "Content-Type: application/json" ^
  -d "{\"email\":\"<EMAIL>\",\"password\":\"admin123456\"}"
```

### **4. Accéder au dashboard (si disponible)**
- Ouvrir http://localhost:3000
- Se connecter avec `<EMAIL>` / `admin123456`

## 🔧 **Configuration WhatsApp (Optionnel)**

Pour connecter de vrais messages WhatsApp, modifiez dans `.env` :

```env
# Remplacer par vos vrais tokens WhatsApp
WHATSAPP_ACCESS_TOKEN=votre_vrai_token
WHATSAPP_PHONE_NUMBER_ID=votre_phone_id
WHATSAPP_BUSINESS_ACCOUNT_ID=votre_business_id
```

## 🐛 **Résolution des Problèmes**

### **Erreur de connexion MySQL**
```
❌ Impossible de se connecter à MySQL
```
**Solutions :**
1. Vérifier que MySQL est démarré
2. Vérifier les identifiants dans `.env`
3. Vérifier que la base de données existe
4. Tester la connexion : `mysql -u root -p`

### **Erreur "Table doesn't exist"**
```
❌ Table 'focep_chatbot.customers' doesn't exist
```
**Solution :**
```cmd
npm run init-db:clean
```

### **Port 3000 déjà utilisé**
```
❌ Error: listen EADDRINUSE :::3000
```
**Solutions :**
1. Changer le port dans `.env` : `PORT=3001`
2. Ou arrêter l'autre processus : `netstat -ano | findstr :3000`

### **Erreur de dépendances**
```cmd
# Nettoyer et réinstaller
rm -rf node_modules package-lock.json
npm install
```

## 📊 **Données de Test Créées**

### **Agent Administrateur**
- Email : `<EMAIL>`
- Mot de passe : `admin123456`
- Rôle : Administrateur

### **Agents de Test**
- `<EMAIL>` / `agent123` (Agent - Yaoundé)
- `<EMAIL>` / `agent123` (Agent - Douala)
- `<EMAIL>` / `agent123` (Superviseur - Bafoussam)

### **Client de Test**
- WhatsApp : `+237670123456`
- Nom : Jean Dupont
- Solde : 150,000 FCFA

## 🎯 **Prochaines Étapes**

1. ✅ **Installation terminée** - Le chatbot fonctionne
2. 🧪 **Tests** - Vérifier toutes les fonctionnalités
3. 📱 **WhatsApp** - Configurer les vrais tokens
4. 🎨 **Personnalisation** - Adapter les réponses
5. 🚀 **Déploiement** - Mettre en production

## 📞 **Support**

Si vous rencontrez des problèmes :
1. Vérifiez les logs dans la console
2. Consultez le fichier `logs/combined.log`
3. Testez la connexion MySQL manuellement
4. Vérifiez que toutes les dépendances sont installées

---

**🎉 Félicitations ! Votre chatbot FOCEP avec MySQL est maintenant opérationnel !**
