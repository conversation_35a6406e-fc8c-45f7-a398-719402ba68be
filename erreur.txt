C:\Users\<USER>\Desktop\Nouveau dossier>node scripts/initDatabase.js
🚀 Initialisation de la base de données FOCEP...
Executing (default): SELECT 1+1 AS result
✅ Connexion à MySQL établie
Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'products' AND TABLE_SCHEMA = 'focep_chatbot'
Executing (default): SHOW INDEX FROM `products` FROM `focep_chatbot`
Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'agencies' AND TABLE_SCHEMA = 'focep_chatbot'
Executing (default): SHOW INDEX FROM `agencies` FROM `focep_chatbot`
Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'faqs' AND TABLE_SCHEMA = 'focep_chatbot'
Executing (default): SHOW INDEX FROM `faqs` FROM `focep_chatbot`
Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'auto_responses' AND TABLE_SCHEMA = 'focep_chatbot'
Executing (default): SHOW INDEX FROM `auto_responses` FROM `focep_chatbot`
Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'agents' AND TABLE_SCHEMA = 'focep_chatbot'
Executing (default): SHOW INDEX FROM `agents` FROM `focep_chatbot`
✅ Modèles synchronisés avec la base de données
📦 Initialisation des produits...
Executing (default): SELECT `id`, `name`, `nameEn`, `category`, `description`, `descriptionEn`, `features`, `isActive`, `availableRegions`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`name` = 'Compte d\'épargne' LIMIT 1;
  ⚠️  Produit existe déjà: Compte d'épargne
Executing (default): SELECT `id`, `name`, `nameEn`, `category`, `description`, `descriptionEn`, `features`, `isActive`, `availableRegions`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`name` = 'Compte courant et compte de dépôt simple' LIMIT 1;
  ⚠️  Produit existe déjà: Compte courant et compte de dépôt simple
Executing (default): SELECT `id`, `name`, `nameEn`, `category`, `description`, `descriptionEn`, `features`, `isActive`, `availableRegions`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`name` = 'Bons de caisse' LIMIT 1;
  ⚠️  Produit existe déjà: Bons de caisse
Executing (default): SELECT `id`, `name`, `nameEn`, `category`, `description`, `descriptionEn`, `features`, `isActive`, `availableRegions`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`name` = 'Crédits aux jeunes entrepreneurs' LIMIT 1;
  ⚠️  Produit existe déjà: Crédits aux jeunes entrepreneurs
Executing (default): SELECT `id`, `name`, `nameEn`, `category`, `description`, `descriptionEn`, `features`, `isActive`, `availableRegions`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`name` = 'Crédits aux commerçants et assimilés' LIMIT 1;
  ⚠️  Produit existe déjà: Crédits aux commerçants et assimilés
Executing (default): SELECT `id`, `name`, `nameEn`, `category`, `description`, `descriptionEn`, `features`, `isActive`, `availableRegions`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`name` = 'Crédits aux fonctionnaires et employés d\'entreprises' LIMIT 1;
  ⚠️  Produit existe déjà: Crédits aux fonctionnaires et employés d'entreprises
Executing (default): SELECT `id`, `name`, `nameEn`, `category`, `description`, `descriptionEn`, `features`, `isActive`, `availableRegions`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`name` = 'Crédit scolaire' LIMIT 1;
  ⚠️  Produit existe déjà: Crédit scolaire
Executing (default): SELECT `id`, `name`, `nameEn`, `category`, `description`, `descriptionEn`, `features`, `isActive`, `availableRegions`, `createdAt`, `updatedAt` FROM `products` AS `Product` WHERE `Product`.`name` = 'Crédit agricole saisonnier' LIMIT 1;
  ⚠️  Produit existe déjà: Crédit agricole saisonnier
🏢 Initialisation des agences...
Executing (default): SELECT `id`, `name`, `code`, `region`, `city`, `address`, `coordinates`, `phone`, `email`, `openingHours`, `services`, `isActive`, `createdAt`, `updatedAt` FROM `agencies` AS `Agency` WHERE `Agency`.`code` = 'YDE_CENTRE';
  ⚠️  Agence existe déjà: FOCEP Yaoundé Centre
Executing (default): SELECT `id`, `name`, `code`, `region`, `city`, `address`, `coordinates`, `phone`, `email`, `openingHours`, `services`, `isActive`, `createdAt`, `updatedAt` FROM `agencies` AS `Agency` WHERE `Agency`.`code` = 'YDE_MOKOLO';
  ⚠️  Agence existe déjà: FOCEP Mokolo
Executing (default): SELECT `id`, `name`, `code`, `region`, `city`, `address`, `coordinates`, `phone`, `email`, `openingHours`, `services`, `isActive`, `createdAt`, `updatedAt` FROM `agencies` AS `Agency` WHERE `Agency`.`code` = 'DLA_NDOKOTI';
  ⚠️  Agence existe déjà: FOCEP Douala Ndokoti
Executing (default): SELECT `id`, `name`, `code`, `region`, `city`, `address`, `coordinates`, `phone`, `email`, `openingHours`, `services`, `isActive`, `createdAt`, `updatedAt` FROM `agencies` AS `Agency` WHERE `Agency`.`code` = 'DLA_AKWA';
  ⚠️  Agence existe déjà: FOCEP Douala Akwa
Executing (default): SELECT `id`, `name`, `code`, `region`, `city`, `address`, `coordinates`, `phone`, `email`, `openingHours`, `services`, `isActive`, `createdAt`, `updatedAt` FROM `agencies` AS `Agency` WHERE `Agency`.`code` = 'DLA_MBOPPI';
  ⚠️  Agence existe déjà: FOCEP Douala Mboppi
Executing (default): SELECT `id`, `name`, `code`, `region`, `city`, `address`, `coordinates`, `phone`, `email`, `openingHours`, `services`, `isActive`, `createdAt`, `updatedAt` FROM `agencies` AS `Agency` WHERE `Agency`.`code` = 'BFS_NDJIEMOUN';
  ⚠️  Agence existe déjà: FOCEP Bafoussam Ndjiemoun
Executing (default): SELECT `id`, `name`, `code`, `region`, `city`, `address`, `coordinates`, `phone`, `email`, `openingHours`, `services`, `isActive`, `createdAt`, `updatedAt` FROM `agencies` AS `Agency` WHERE `Agency`.`code` = 'DLA_BONABERI';
  ⚠️  Agence existe déjà: FOCEP Douala Bonabéri
Executing (default): SELECT `id`, `name`, `code`, `region`, `city`, `address`, `coordinates`, `phone`, `email`, `openingHours`, `services`, `isActive`, `createdAt`, `updatedAt` FROM `agencies` AS `Agency` WHERE `Agency`.`code` = 'BAZ_CENTRE';
  ⚠️  Agence existe déjà: FOCEP Bazou
❓ Initialisation des FAQ...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Qu\'est-ce que FOCEP SA ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Qu'est-ce que FOCEP SA ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Quels sont vos horaires d\'ouverture ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Quels sont vos horaires d'ouverture ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Comment ouvrir un compte chez FOCEP ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Comment ouvrir un compte chez FOCEP ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Quel est le taux d\'intérêt sur l\'épargne ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Quel est le taux d'intérêt sur l'épargne ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Quels types de crédits proposez-vous ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Quels types de crédits proposez-vous ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Comment faire une demande de crédit ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Comment faire une demande de crédit ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Proposez-vous des services de mobile money ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Proposez-vous des services de mobile money ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Où se trouvent vos agences ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Où se trouvent vos agences ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Quels sont les frais de tenue de compte ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Quels sont les frais de tenue de compte ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Comment puis-je faire un virement ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Comment puis-je faire un virement ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Quel est le montant minimum pour ouvrir un compte d\'épargne ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Quel est le montant minimum pour ouvrir un compte ...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Combien de temps faut-il pour obtenir un crédit ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Combien de temps faut-il pour obtenir un crédit ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Proposez-vous des assurances ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Proposez-vous des assurances ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Comment puis-je consulter mon solde ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Comment puis-je consulter mon solde ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Quels sont vos partenaires mobile money ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Quels sont vos partenaires mobile money ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Quels sont les services spéciaux de FOCEP SA ?' LIMIT 1;
  ⚠️  FAQ existe déjà: Quels sont les services spéciaux de FOCEP SA ?...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Où FOCEP SA prévoit-elle d\'ouvrir de nouvelles agences ?' LIMIT 1;
Executing (default): INSERT INTO `faqs` (`id`,`question`,`questionEn`,`answer`,`answerEn`,`category`,`keywords`,`usageCount`,`isActive`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?);
  ✅ FAQ créée: Où FOCEP SA prévoit-elle d'ouvrir de nouvelles age...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Quel est le classement de FOCEP SA parmi les EMF du Cameroun ?' LIMIT 1;
Executing (default): INSERT INTO `faqs` (`id`,`question`,`questionEn`,`answer`,`answerEn`,`category`,`keywords`,`usageCount`,`isActive`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?);
  ✅ FAQ créée: Quel est le classement de FOCEP SA parmi les EMF d...
Executing (default): SELECT `id`, `question`, `questionEn`, `answer`, `answerEn`, `category`, `keywords`, `usageCount`, `isActive`, `createdAt`, `updatedAt` FROM `faqs` AS `FAQ` WHERE `FAQ`.`question` = 'Comment FOCEP SA se distingue-t-elle de la concurrence ?' LIMIT 1;
Executing (default): INSERT INTO `faqs` (`id`,`question`,`questionEn`,`answer`,`answerEn`,`category`,`keywords`,`usageCount`,`isActive`,`createdAt`,`updatedAt`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?);
  ✅ FAQ créée: Comment FOCEP SA se distingue-t-elle de la concurr...
🤖 Initialisation des réponses automatiques...
Executing (default): SELECT `id`, `trigger`, `response`, `conditions`, `stats`, `isActive`, `createdAt`, `updatedAt` FROM `auto_responses` AS `AutoResponse` WHERE JSON_EXTRACT(trigger, '$.intent') = 'salutation' LIMIT 1;
❌ Erreur lors de l'initialisation: Error
    at Query.run (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\sequelize\lib\dialects\mysql\query.js:52:25)
    at C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\sequelize\lib\sequelize.js:315:28
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async MySQLQueryInterface.select (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\sequelize\lib\dialects\abstract\query-interface.js:407:12)
    at async AutoResponse.findAll (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\sequelize\lib\model.js:1140:21)
    at async AutoResponse.findOne (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\sequelize\lib\model.js:1240:12)
    at async initializeDatabase (C:\Users\<USER>\Desktop\Nouveau dossier\scripts\initDatabase.js:76:32) {
  name: 'SequelizeDatabaseError',
  parent: Error: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'trigger, '$.intent') = 'salutation' LIMIT 1' at line 1
      at Packet.asError (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\packets\packet.js:740:17)
      at Query.execute (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\commands\command.js:29:26)
      at Connection.handlePacket (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\base\connection.js:475:34)
      at PacketParser.onPacket (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\base\connection.js:93:12)
      at PacketParser.executeStart (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\packet_parser.js:75:16)
      at Socket.<anonymous> (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\base\connection.js:100:25)
      at Socket.emit (node:events:518:28)
      at addChunk (node:internal/streams/readable:561:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
      at Readable.push (node:internal/streams/readable:392:5) {
    code: 'ER_PARSE_ERROR',
    errno: 1064,
    sqlState: '42000',
    sqlMessage: "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'trigger, '$.intent') = 'salutation' LIMIT 1' at line 1",
    sql: "SELECT `id`, `trigger`, `response`, `conditions`, `stats`, `isActive`, `createdAt`, `updatedAt` FROM `auto_responses` AS `AutoResponse` WHERE JSON_EXTRACT(trigger, '$.intent') = 'salutation' LIMIT 1;",
    parameters: undefined
  },
  original: Error: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'trigger, '$.intent') = 'salutation' LIMIT 1' at line 1
      at Packet.asError (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\packets\packet.js:740:17)
      at Query.execute (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\commands\command.js:29:26)
      at Connection.handlePacket (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\base\connection.js:475:34)
      at PacketParser.onPacket (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\base\connection.js:93:12)
      at PacketParser.executeStart (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\packet_parser.js:75:16)
      at Socket.<anonymous> (C:\Users\<USER>\Desktop\Nouveau dossier\node_modules\mysql2\lib\base\connection.js:100:25)
      at Socket.emit (node:events:518:28)
      at addChunk (node:internal/streams/readable:561:12)
      at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
      at Readable.push (node:internal/streams/readable:392:5) {
    code: 'ER_PARSE_ERROR',
    errno: 1064,
    sqlState: '42000',
    sqlMessage: "You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'trigger, '$.intent') = 'salutation' LIMIT 1' at line 1",
    sql: "SELECT `id`, `trigger`, `response`, `conditions`, `stats`, `isActive`, `createdAt`, `updatedAt` FROM `auto_responses` AS `AutoResponse` WHERE JSON_EXTRACT(trigger, '$.intent') = 'salutation' LIMIT 1;",
    parameters: undefined
  },
  sql: "SELECT `id`, `trigger`, `response`, `conditions`, `stats`, `isActive`, `createdAt`, `updatedAt` FROM `auto_responses` AS `AutoResponse` WHERE JSON_EXTRACT(trigger, '$.intent') = 'salutation' LIMIT 1;",
  parameters: {}
}
🔌 Connexion MySQL fermée