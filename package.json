{"name": "focep-whatsapp-chatbot", "version": "1.0.0", "description": "Intelligent WhatsApp chatbot for FOCEP SA microfinance customer service", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:coverage": "jest --coverage --detect<PERSON><PERSON>Handles --forceExit", "test:integration": "node scripts/testSystem.js", "test:deepseek": "jest tests/integration/deepSeekIntegration.test.js --detectOpenHandles --forceExit", "validate:deepseek": "node scripts/validateDeepSeekIntegration.js", "init-db": "node scripts/initMySQLDatabase.js", "init-db:clean": "node scripts/initMySQLDatabase.js --clean", "build": "npm run build:client", "build:client": "cd client && npm run build", "install:client": "cd client && npm install", "heroku-postbuild": "npm run install:client && npm run build:client"}, "keywords": ["whatsapp", "chatbot", "microfinance", "focep", "cameroon", "customer-service", "nlp"], "author": "FOCEP SA", "license": "MIT", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "cheerio": "^1.1.2", "compromise": "^14.10.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "franc": "^6.1.0", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.2", "natural": "^6.5.0", "node-cron": "^3.0.3", "qrcode-terminal": "^0.12.0", "sequelize": "^6.37.7", "sharp": "^0.32.5", "socket.io": "^4.7.2", "whatsapp-web.js": "^1.31.0", "winston": "^3.10.0"}, "devDependencies": {"@types/jest": "^29.5.5", "colors": "^1.4.0", "jest": "^29.6.4", "mongodb-memory-server": "^8.15.1", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}