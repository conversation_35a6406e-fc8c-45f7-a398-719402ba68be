const { products, agencies, faqs, autoResponses, companyInfo } = require('./data/focepData');
const ResponseGenerator = require('./services/responseGenerator');

console.log('Test des nouvelles donnees FOCEP SA enrichies\n');

// Test des informations generales de l'entreprise
console.log('INFORMATIONS GENERALES DE L\'ENTREPRISE:');
console.log('Nom complet: ' + companyInfo.fullName);
console.log('Annee de creation: ' + companyInfo.yearFounded);
console.log('Annees d\'existence: ' + companyInfo.yearsOfExistence + ' ans');
console.log('Nombre total d\'agences: ' + companyInfo.totalAgencies);
console.log('Nombre de clients: ' + companyInfo.currentClients.toLocaleString());
console.log('Ressources totales: ' + companyInfo.totalResources);
console.log('Lignes de credit: ' + companyInfo.creditLines);
console.log('Classement: ' + companyInfo.ranking);
console.log('Objectif 2027: ' + companyInfo.objective2027);
console.log('PCA: ' + companyInfo.leadership.pca);
console.log('DG: ' + companyInfo.leadership.dg);
console.log('Telephone: ' + companyInfo.contact.phone);
console.log('Email: ' + companyInfo.contact.email);
console.log('Site web: ' + companyInfo.contact.website);
console.log('Regions presentes: ' + companyInfo.regionsPresent.join(', '));
console.log('Services speciaux: ' + companyInfo.specialServices.join(', '));

// Test des nouvelles agences
console.log('\nNOUVELLES AGENCES:');
const newAgencies = agencies.filter(agency => agency.dateOpening);
newAgencies.forEach(agency => {
  console.log('- ' + agency.name + ' (' + agency.city + ')');
  console.log('  Ouverte le: ' + agency.dateOpening);
  console.log('  Adresse: ' + agency.address);
  console.log('  Telephone: ' + agency.phone);
  console.log('  Services: ' + agency.services.join(', '));
  if (agency.specialties) {
    console.log('  Specialites: ' + agency.specialties.join(', '));
  }
  console.log('');
});

// Test des nouvelles FAQs
console.log('NOUVELLES FAQs:');
const newFaqs = faqs.filter(faq =>
  faq.keywords.includes('collecte journaliere') ||
  faq.keywords.includes('expansion') ||
  faq.keywords.includes('classement') ||
  faq.keywords.includes('50000 clients')
);
newFaqs.forEach((faq, index) => {
  console.log((index + 1) + '. ' + faq.question);
  console.log('   Reponse: ' + faq.answer.substring(0, 100) + '...');
  console.log('   Mots-cles: ' + faq.keywords.join(', '));
  console.log('');
});

// Test des nouvelles donnees integrees (juillet 2025)
console.log('NOUVELLES DONNEES INTEGREES (JUILLET 2025):');
console.log('Effectif: ' + companyInfo.totalEmployees + ' employes (vs 300 initialement)');
console.log('Agences: ' + companyInfo.totalAgencies + ' agences (vs 14 initialement)');
console.log('Capital: ' + companyInfo.capital.current2021);
console.log('Contexte marche: ' + companyInfo.marketContext);
console.log('PCA: ' + companyInfo.leadership.pca.name + ' - ' + companyInfo.leadership.pca.profile);
console.log('DG: ' + companyInfo.leadership.dg.name + ' - ' + companyInfo.leadership.dg.experience);
console.log('Partenariat: ' + companyInfo.partnerships[0].name + ' depuis ' + companyInfo.partnerships[0].since);
console.log('Services cibles: ' + companyInfo.targetClients.join(', '));
console.log('Avantages concurrentiels: ' + companyInfo.competitiveAdvantages.join(', '));

// Test du generateur de reponses avec les nouvelles donnees
console.log('\nTEST DU GENERATEUR DE REPONSES:');
try {
  const ResponseGeneratorClass = require('./services/responseGenerator');
  const responseGenerator = new ResponseGeneratorClass();

  // Test de la reponse de contact mise a jour
  console.log('\nReponse de contact:');
  const contactResponse = responseGenerator.generateContactResponse('fr');
  console.log(contactResponse.content.text);

  // Test de la nouvelle reponse d'informations sur l'entreprise
  console.log('\nReponse informations entreprise enrichie:');
  const companyResponse = responseGenerator.generateCompanyInfoResponse('fr');
  console.log(companyResponse.content.text);
} catch (error) {
  console.log('Erreur lors du test du generateur de reponses:', error.message);
  console.log('Les nouvelles donnees sont integrees mais le test du generateur necessite une configuration complete.');
}

// Statistiques finales
console.log('\nSTATISTIQUES FINALES:');
console.log('Total produits: ' + products.length);
console.log('Total agences: ' + agencies.length);
console.log('Total FAQs: ' + faqs.length);
console.log('Total auto-reponses: ' + autoResponses.length);

// Verification des services speciaux dans les agences
const agenciesWithSpecialServices = agencies.filter(agency =>
  agency.services.includes('collecte_journaliere') ||
  agency.services.includes('tontine')
);
console.log('Agences avec services speciaux (collecte/tontine): ' + agenciesWithSpecialServices.length);

console.log('\nTest termine - Toutes les nouvelles donnees FOCEP SA sont integrees !');
