# 🤖 FOCEP WhatsApp Chatbot Intelligent

Un chatbot WhatsApp moderne et intelligent pour FOCEP SA (Fonds Camerounais d'Épargne pour le Progrès), conçu pour offrir un service client automatisé de haute qualité avec des capacités de traitement du langage naturel en français et anglais.

## 🌟 Fonctionnalités Principales

### 💬 Chatbot Intelligent
- **Traitement du langage naturel** avancé en français et anglais
- **Détection automatique de la langue** avec adaptation des réponses
- **Analyse de sentiment** pour détecter la satisfaction client
- **Escalade intelligente** vers les agents humains quand nécessaire
- **Gestion contextuelle** des conversations multi-tours

### 🏦 Spécificités FOCEP
- **Informations sur les produits** d'épargne et de financement
- **Localisation des agences** (Yaoundé, Douala, Bafoussam)
- **Statut des comptes et prêts** pour les clients existants
- **Processus de demande de crédit** guidé
- **Services mobile money** et microassurance
- **Horaires et contacts** des agences

### 🔧 Fonctionnalités Techniques
- **API WhatsApp Business** intégrée avec webhooks sécurisés
- **Base de données MongoDB** avec modèles optimisés
- **Système d'authentification JWT** pour les agents
- **Dashboard administrateur** React pour la gestion
- **Rate limiting** et protection contre le spam
- **Logging avancé** avec Winston
- **Tests automatisés** complets avec Jest

## 🚀 Installation et Configuration

### Prérequis
- Node.js 16+ et npm 8+
- MongoDB 4.4+
- Compte WhatsApp Business API
- Compte développeur Facebook

### 1. Cloner le projet
```bash
git clone <repository-url>
cd focep-whatsapp-chatbot
npm install
```

### 2. Configuration des variables d'environnement
```bash
cp .env.example .env
```

Éditer le fichier `.env` avec vos configurations :

```env
# Configuration serveur
PORT=3000
NODE_ENV=development

# Base de données
MONGODB_URI=mongodb://localhost:27017/focep-chatbot

# WhatsApp Business API
WHATSAPP_ACCESS_TOKEN=your_access_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_verify_token
WHATSAPP_WEBHOOK_SECRET=your_webhook_secret

# JWT et sécurité
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRE=7d

# Configuration admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123456
```

### 3. Initialiser la base de données
```bash
# Initialiser avec les données FOCEP
npm run init-db

# Ou nettoyer et réinitialiser
npm run init-db:clean
```

### 4. Démarrer l'application
```bash
# Mode développement
npm run dev

# Mode production
npm start
```

## 🧪 Tests et Qualité

### Tests unitaires
```bash
# Exécuter tous les tests
npm test

# Tests en mode watch
npm run test:watch

# Tests avec couverture
npm run test:coverage
```

### Tests d'intégration
```bash
# Test complet du système
npm run test:integration
```

### Structure des tests
```
tests/
├── setup.js                 # Configuration Jest
├── services/
│   ├── messageProcessor.test.js
│   ├── nlpService.test.js
│   └── responseGenerator.test.js
├── utils/
│   ├── validators.test.js
│   └── errors.test.js
└── routes/
    ├── auth.test.js
    └── whatsapp.test.js
```

## 📁 Architecture du Projet

```
focep-whatsapp-chatbot/
├── server.js                # Point d'entrée principal
├── package.json             # Dépendances et scripts
├── .env.example             # Template de configuration
├── README.md               # Documentation
│
├── middleware/             # Middlewares Express
│   ├── auth.js            # Authentification JWT
│   └── errorHandler.js    # Gestion globale des erreurs
│
├── models/                # Modèles MongoDB/Mongoose
│   ├── Customer.js        # Modèle client
│   ├── Conversation.js    # Modèle conversation
│   ├── Agent.js          # Modèle agent
│   └── FocepData.js      # Produits, agences, FAQ
│
├── routes/               # Routes API Express
│   ├── whatsapp.js      # Webhooks WhatsApp
│   ├── auth.js          # Authentification agents
│   ├── conversations.js # Gestion conversations
│   └── analytics.js     # Statistiques
│
├── services/            # Logique métier
│   ├── messageProcessor.js    # Traitement messages
│   ├── nlpService.js          # Analyse NLP
│   ├── responseGenerator.js   # Génération réponses
│   ├── whatsappService.js     # API WhatsApp
│   └── cronJobs.js           # Tâches programmées
│
├── utils/              # Utilitaires
│   ├── logger.js       # Logging Winston
│   ├── validators.js   # Validation données
│   └── errors.js       # Gestion erreurs
│
├── data/              # Données initiales
│   └── focepData.js   # Produits et agences FOCEP
│
├── scripts/           # Scripts utilitaires
│   ├── initDatabase.js # Initialisation DB
│   └── testSystem.js   # Tests intégration
│
├── tests/             # Tests automatisés
│   ├── setup.js       # Configuration Jest
│   ├── services/      # Tests services
│   ├── utils/         # Tests utilitaires
│   └── routes/        # Tests routes
│
├── client/            # Dashboard React (optionnel)
│   ├── src/
│   ├── public/
│   └── package.json
│
└── logs/              # Fichiers de logs
    ├── combined.log
    └── error.log
```

## 🔧 Configuration WhatsApp Business API

### 1. Créer une application Facebook
1. Aller sur [Facebook Developers](https://developers.facebook.com/)
2. Créer une nouvelle application
3. Ajouter le produit "WhatsApp Business API"

### 2. Configurer le webhook
- URL du webhook : `https://votre-domaine.com/api/whatsapp/webhook`
- Token de vérification : celui défini dans `WHATSAPP_WEBHOOK_VERIFY_TOKEN`
- Champs d'abonnement : `messages`, `message_deliveries`, `message_reads`

### 3. Obtenir les tokens
- Access Token : depuis la console Facebook Developers
- Phone Number ID : ID du numéro WhatsApp Business
- Business Account ID : ID du compte WhatsApp Business

## 🎯 Utilisation

### Interface Chatbot
Les clients peuvent interagir avec le chatbot via WhatsApp en envoyant des messages au numéro configuré. Le bot comprend :

**Salutations :**
- "Bonjour", "Salut", "Hello"

**Informations compte :**
- "Mon solde", "Balance", "Combien j'ai"

**Statut prêt :**
- "Mon prêt", "Statut crédit", "Remboursement"

**Informations générales :**
- "Vos agences", "Horaires", "Contact"
- "Vos produits", "Services", "Taux d'intérêt"

### Dashboard Administrateur
Accès via `http://localhost:3000` avec les identifiants admin configurés.

Fonctionnalités :
- Monitoring des conversations en temps réel
- Gestion des agents et assignations
- Statistiques et analytics
- Configuration des réponses automatiques

## 🔒 Sécurité

### Mesures implémentées
- **Validation stricte** de toutes les entrées utilisateur
- **Rate limiting** pour prévenir le spam
- **Authentification JWT** pour les agents
- **Chiffrement des mots de passe** avec bcrypt
- **Validation des webhooks** WhatsApp
- **Sanitisation** des contenus utilisateur
- **Logging sécurisé** sans données sensibles

### Bonnes pratiques
- Utiliser HTTPS en production
- Configurer des mots de passe forts
- Limiter les permissions des agents
- Surveiller les logs d'erreur
- Sauvegarder régulièrement la base de données

## 📊 Monitoring et Analytics

### Métriques collectées
- Nombre de conversations par jour/semaine/mois
- Temps de réponse moyen
- Taux de satisfaction client
- Intentions les plus fréquentes
- Performance des agents
- Taux d'escalade vers agents humains

### Logs disponibles
- `logs/combined.log` : Tous les événements
- `logs/error.log` : Erreurs uniquement
- Logs structurés en JSON pour analyse

## 🚀 Déploiement

### Déploiement Heroku
```bash
# Créer l'application Heroku
heroku create focep-chatbot

# Configurer les variables d'environnement
heroku config:set NODE_ENV=production
heroku config:set MONGODB_URI=your_mongodb_atlas_uri
heroku config:set WHATSAPP_ACCESS_TOKEN=your_token
# ... autres variables

# Déployer
git push heroku main

# Initialiser la base de données
heroku run npm run init-db
```

### Déploiement Docker
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 Contribution

### Standards de code
- ESLint pour la qualité du code
- Prettier pour le formatage
- Tests obligatoires pour nouvelles fonctionnalités
- Documentation des fonctions complexes

### Processus de contribution
1. Fork du projet
2. Créer une branche feature
3. Développer avec tests
4. Soumettre une Pull Request

## 📞 Support

Pour toute question ou problème :
- **Email technique :** <EMAIL>
- **Documentation :** Voir le wiki du projet
- **Issues :** Utiliser le système d'issues GitHub

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

---

**FOCEP SA** - Fonds Camerounais d'Épargne pour le Progrès  
*Votre partenaire financier de confiance au Cameroun* 🇨🇲
